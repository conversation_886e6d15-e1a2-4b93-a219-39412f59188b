namespace AlpacaMomentumBot.Console.Services;

public interface IPortfolioGate
{
    Task<PortfolioStatus> GetPortfolioStatusAsync(CancellationToken cancellationToken = default);
    Task<bool> CanExecuteTradeAsync(TradingSignal signal, RiskAssessment risk, CancellationToken cancellationToken = default);
}

public record PortfolioStatus(
    decimal? TotalValue,
    decimal? AvailableCash,
    decimal? DayTradingBuyingPower,
    int OpenPositions,
    bool IsMarketOpen);
