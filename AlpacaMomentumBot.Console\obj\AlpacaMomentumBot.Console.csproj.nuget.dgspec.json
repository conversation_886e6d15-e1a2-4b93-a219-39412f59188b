{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive\\AugmentCode\\SmaTrendFollower\\AlpacaMomentumBot.Console\\AlpacaMomentumBot.Console.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive\\AugmentCode\\SmaTrendFollower\\AlpacaMomentumBot.Console\\AlpacaMomentumBot.Console.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive\\AugmentCode\\SmaTrendFollower\\AlpacaMomentumBot.Console\\AlpacaMomentumBot.Console.csproj", "projectName": "AlpacaMomentumBot.Console", "projectPath": "C:\\Users\\<USER>\\OneDrive\\AugmentCode\\SmaTrendFollower\\AlpacaMomentumBot.Console\\AlpacaMomentumBot.Console.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive\\AugmentCode\\SmaTrendFollower\\AlpacaMomentumBot.Console\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Alpaca.Markets": {"target": "Package", "version": "[7.2.0, )"}, "Cronos": {"target": "Package", "version": "[0.11.0, )"}, "DotNetEnv": {"target": "Package", "version": "[3.1.1, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}, "Skender.Stock.Indicators": {"target": "Package", "version": "[2.6.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}