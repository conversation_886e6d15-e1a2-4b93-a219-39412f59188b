using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Services;

public class AlpacaClientFactory : IAlpacaClientFactory
{
    private readonly ILogger<AlpacaClientFactory> _logger;

    public AlpacaClientFactory(ILogger<AlpacaClientFactory> logger)
    {
        _logger = logger;
    }

    public IAlpacaTradingClient CreateTradingClient()
    {
        var apiKey = Environment.GetEnvironmentVariable("ALPACA_API_KEY");
        var secretKey = Environment.GetEnvironmentVariable("ALPACA_SECRET_KEY");
        var isPaper = Environment.GetEnvironmentVariable("ALPACA_PAPER") == "true";

        if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(secretKey))
        {
            throw new InvalidOperationException("Alpaca API credentials not found in environment variables");
        }

        _logger.LogInformation("Creating Alpaca trading client (Paper: {IsPaper})", isPaper);

        var environment = isPaper ? Environments.Paper : Environments.Live;
        
        return environment.GetAlpacaTradingClient(new SecretKey(apiKey, secretKey));
    }

    public IAlpacaDataClient CreateDataClient()
    {
        var apiKey = Environment.GetEnvironmentVariable("ALPACA_API_KEY");
        var secretKey = Environment.GetEnvironmentVariable("ALPACA_SECRET_KEY");

        if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(secretKey))
        {
            throw new InvalidOperationException("Alpaca API credentials not found in environment variables");
        }

        _logger.LogInformation("Creating Alpaca data client");

        return Environments.Live.GetAlpacaDataClient(new SecretKey(apiKey, secretKey));
    }
}
