using Cronos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Services;

public class ScheduleService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ScheduleService> _logger;
    private readonly CronExpression _cronExpression;
    private readonly TimeZoneInfo _timeZone;

    public ScheduleService(IServiceProvider serviceProvider, ILogger<ScheduleService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        
        // Cron expression for 16:10 (4:10 PM) on weekdays (Monday-Friday)
        // Format: minute hour day-of-month month day-of-week
        // 10 16 * * 1-5 = 10th minute, 16th hour (4 PM), any day of month, any month, Monday-Friday
        _cronExpression = CronExpression.Parse("10 16 * * 1-5");
        
        // America/New_York timezone
        _timeZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");
        
        _logger.LogInformation("ScheduleService initialized. Next run: {NextRun}", GetNextRunTime());
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("ScheduleService started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var nextRun = GetNextRunTime();
                var delay = nextRun - DateTimeOffset.UtcNow;

                if (delay > TimeSpan.Zero)
                {
                    _logger.LogInformation("Next trading cycle scheduled for {NextRun} (in {Delay})", 
                        nextRun, delay);
                    
                    await Task.Delay(delay, stoppingToken);
                }

                if (!stoppingToken.IsCancellationRequested)
                {
                    await ExecuteTradingCycleAsync(stoppingToken);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("ScheduleService cancellation requested");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ScheduleService execution");
                
                // Wait 1 minute before retrying to avoid rapid failures
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }

        _logger.LogInformation("ScheduleService stopped");
    }

    private DateTimeOffset GetNextRunTime()
    {
        var utcNow = DateTime.UtcNow;
        var nextUtc = _cronExpression.GetNextOccurrence(utcNow, _timeZone);

        return nextUtc ?? DateTimeOffset.UtcNow.AddDays(1); // Fallback to tomorrow if no next occurrence
    }

    private async Task ExecuteTradingCycleAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Executing scheduled trading cycle at {Time}", DateTime.UtcNow);

            using var scope = _serviceProvider.CreateScope();
            var tradingService = scope.ServiceProvider.GetRequiredService<ITradingService>();
            
            await tradingService.TradeCycleAsync(cancellationToken);
            
            _logger.LogInformation("Scheduled trading cycle completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing scheduled trading cycle");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("ScheduleService stop requested");
        await base.StopAsync(cancellationToken);
    }
}
