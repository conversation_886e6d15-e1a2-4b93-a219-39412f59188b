using AlpacaMomentumBot.Console.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace AlpacaMomentumBot.Tests;

public class RiskManagerTests
{
    private readonly Mock<ILogger<RiskManager>> _loggerMock;
    private readonly RiskManager _riskManager;

    public RiskManagerTests()
    {
        _loggerMock = new Mock<ILogger<RiskManager>>();
        _riskManager = new RiskManager(_loggerMock.Object);
    }

    [Fact]
    public async Task AssessRiskAsync_WithNoSignal_ShouldRejectTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.None, 0, DateTime.UtcNow, "No signal");
        var portfolioValue = 10000m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeFalse();
        result.RiskReason.Should().Be("No trading signal");
    }

    [Fact]
    public async Task AssessRiskAsync_WithLowConfidence_ShouldRejectTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.2m, DateTime.UtcNow, "Low confidence");
        var portfolioValue = 10000m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeFalse();
        result.RiskReason.Should().Contain("Signal confidence too low");
    }

    [Fact]
    public async Task AssessRiskAsync_WithValidSignal_ShouldApproveTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Strong buy signal");
        var portfolioValue = 10000m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeTrue();
        result.MaxPositionSize.Should().BeGreaterThan(0);
        result.StopLossPrice.Should().BeGreaterThan(0);
        result.TakeProfitPrice.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task AssessRiskAsync_WithZeroPortfolioValue_ShouldRejectTrade()
    {
        // Arrange
        var signal = new TradingSignal("AAPL", SignalType.Buy, 0.8m, DateTime.UtcNow, "Strong buy signal");
        var portfolioValue = 0m;

        // Act
        var result = await _riskManager.AssessRiskAsync(signal, portfolioValue);

        // Assert
        result.IsApproved.Should().BeFalse();
        result.RiskReason.Should().Be("Invalid portfolio value");
    }
}