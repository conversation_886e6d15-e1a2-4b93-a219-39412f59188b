using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Services;

public class TradingService : ITradingService
{
    private readonly ISignalGenerator _signalGenerator;
    private readonly IRiskManager _riskManager;
    private readonly IPortfolioGate _portfolioGate;
    private readonly ITradeExecutor _tradeExecutor;
    private readonly ILogger<TradingService> _logger;

    // Default symbols to trade - can be moved to configuration
    private readonly string[] _symbols = { "SPY", "QQQ", "AAPL", "MSFT", "GOOGL" };

    public TradingService(
        ISignalGenerator signalGenerator,
        IRiskManager riskManager,
        IPortfolioGate portfolioGate,
        ITradeExecutor tradeExecutor,
        ILogger<TradingService> logger)
    {
        _signalGenerator = signalGenerator;
        _riskManager = riskManager;
        _portfolioGate = portfolioGate;
        _tradeExecutor = tradeExecutor;
        _logger = logger;
    }

    public async Task TradeCycleAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting trading cycle at {Time}", DateTime.UtcNow);

            // Get portfolio status
            var portfolioStatus = await _portfolioGate.GetPortfolioStatusAsync(cancellationToken);
            
            if (!portfolioStatus.IsMarketOpen)
            {
                _logger.LogInformation("Market is closed, skipping trading cycle");
                return;
            }

            _logger.LogInformation("Portfolio Value: {Value:C}, Available Cash: {Cash:C}, Open Positions: {Positions}",
                portfolioStatus.TotalValue ?? 0m, portfolioStatus.AvailableCash ?? 0m, portfolioStatus.OpenPositions);

            // Process each symbol
            foreach (var symbol in _symbols)
            {
                try
                {
                    await ProcessSymbolAsync(symbol, portfolioStatus.TotalValue, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing symbol {Symbol}", symbol);
                    // Continue with next symbol
                }
            }

            // NOTE: Alternative approach using the new RunAsync method:
            // var topSignals = await _signalGenerator.RunAsync(10, cancellationToken);
            // foreach (var signal in topSignals)
            // {
            //     try
            //     {
            //         await ProcessSignalAsync(signal, portfolioStatus.TotalValue, cancellationToken);
            //     }
            //     catch (Exception ex)
            //     {
            //         _logger.LogError(ex, "Error processing signal for {Symbol}", signal.Symbol);
            //     }
            // }

            _logger.LogInformation("Trading cycle completed at {Time}", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in trading cycle");
            throw;
        }
    }

    private async Task ProcessSymbolAsync(string symbol, decimal? portfolioValue, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing symbol: {Symbol}", symbol);

        // Generate trading signal
        var signal = await _signalGenerator.GenerateSignalAsync(symbol, cancellationToken);
        
        if (signal.Type == SignalType.None)
        {
            _logger.LogInformation("No signal for {Symbol}: {Reason}", symbol, signal.Reason);
            return;
        }

        _logger.LogInformation("Signal generated for {Symbol}: {Type} (Confidence: {Confidence:P})", 
            symbol, signal.Type, signal.Confidence);

        // Assess risk
        var riskAssessment = await _riskManager.AssessRiskAsync(signal, portfolioValue ?? 0m, cancellationToken);
        
        if (!riskAssessment.IsApproved)
        {
            _logger.LogInformation("Trade rejected for {Symbol}: {Reason}", symbol, riskAssessment.RiskReason);
            return;
        }

        // Check portfolio constraints
        var canExecute = await _portfolioGate.CanExecuteTradeAsync(signal, riskAssessment, cancellationToken);
        
        if (!canExecute)
        {
            _logger.LogInformation("Trade blocked by portfolio gate for {Symbol}", symbol);
            return;
        }

        // Execute trade
        var tradeResult = await _tradeExecutor.ExecuteTradeAsync(signal, riskAssessment, cancellationToken);
        
        if (tradeResult.IsSuccessful)
        {
            _logger.LogInformation("Trade executed successfully for {Symbol}: {Message}", symbol, tradeResult.Message);
        }
        else
        {
            _logger.LogWarning("Trade failed for {Symbol}: {Message}", symbol, tradeResult.Message);
        }
    }
}
