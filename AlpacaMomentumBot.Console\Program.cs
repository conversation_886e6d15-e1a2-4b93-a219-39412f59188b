﻿using AlpacaMomentumBot.Console.Services;
using Cronos;
using DotNetEnv;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Globalization;

// Load environment variables
Env.Load();

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/alpaca-momentum-bot-.log",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30)
    .CreateLogger();

try
{
    Log.Information("Starting Alpaca Momentum Bot");

    var host = Host.CreateDefaultBuilder(args)
        .UseSerilog()
        .ConfigureServices((context, services) =>
        {
            // Register services
            services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
            services.AddSingleton<IUniverseProvider, UniverseProvider>();
            services.AddScoped<ISignalGenerator, SignalGenerator>();
            services.AddScoped<IRiskManager, RiskManager>();
            services.AddScoped<IPortfolioGate, PortfolioGate>();
            services.AddScoped<ITradeExecutor, TradeExecutor>();
            services.AddScoped<ITradingService, TradingService>();

            // Register the scheduled service
            services.AddHostedService<ScheduleService>();
        })
        .Build();

    await host.RunAsync();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
