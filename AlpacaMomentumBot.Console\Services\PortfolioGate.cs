using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Console.Services;

public class PortfolioGate : IPortfolioGate
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<PortfolioGate> _logger;

    public PortfolioGate(IAlpacaClientFactory clientFactory, ILogger<PortfolioGate> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task<PortfolioStatus> GetPortfolioStatusAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Getting portfolio status");

            using var tradingClient = _clientFactory.CreateTradingClient();

            // Get account information
            var account = await tradingClient.GetAccountAsync(cancellationToken);
            
            // Get market status
            var clock = await tradingClient.GetClockAsync(cancellationToken);
            
            // Get open positions
            var positions = await tradingClient.ListPositionsAsync(cancellationToken);

            var status = new PortfolioStatus(
                TotalValue: account.Equity,
                AvailableCash: account.TradableCash,
                DayTradingBuyingPower: account.DayTradingBuyingPower,
                OpenPositions: positions.Count,
                IsMarketOpen: clock.IsOpen
            );

            _logger.LogInformation("Portfolio Status - Value: {Value:C}, Cash: {Cash:C}, Positions: {Positions}, Market Open: {IsOpen}",
                status.TotalValue, status.AvailableCash, status.OpenPositions, status.IsMarketOpen);

            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting portfolio status");
            throw;
        }
    }

    public async Task<bool> CanExecuteTradeAsync(TradingSignal signal, RiskAssessment risk, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Checking if trade can be executed for {Symbol}", signal.Symbol);

            if (!risk.IsApproved)
            {
                _logger.LogWarning("Trade rejected by risk assessment: {Reason}", risk.RiskReason);
                return false;
            }

            var portfolioStatus = await GetPortfolioStatusAsync(cancellationToken);

            if (!portfolioStatus.IsMarketOpen)
            {
                _logger.LogWarning("Market is closed, cannot execute trade");
                return false;
            }

            // Check if we have sufficient buying power
            var estimatedTradeValue = risk.MaxPositionSize * 100m; // Placeholder price
            var availableBuyingPower = portfolioStatus.DayTradingBuyingPower ?? 0m;
            if (estimatedTradeValue > availableBuyingPower)
            {
                _logger.LogWarning("Insufficient buying power. Need: {Need:C}, Available: {Available:C}",
                    estimatedTradeValue, availableBuyingPower);
                return false;
            }

            // Check position limits (e.g., max 10 open positions)
            if (portfolioStatus.OpenPositions >= 10)
            {
                _logger.LogWarning("Too many open positions: {Count}", portfolioStatus.OpenPositions);
                return false;
            }

            using var tradingClient = _clientFactory.CreateTradingClient();
            
            // Check if we already have a position in this symbol
            try
            {
                var existingPosition = await tradingClient.GetPositionAsync(signal.Symbol, cancellationToken);
                if (existingPosition != null && Math.Abs(existingPosition.Quantity) > 0)
                {
                    _logger.LogWarning("Already have position in {Symbol}: {Quantity} shares",
                        signal.Symbol, existingPosition.Quantity);
                    return false;
                }
            }
            catch (Alpaca.Markets.RestClientErrorException)
            {
                // No existing position - this is fine, continue
                _logger.LogInformation("No existing position in {Symbol}, trade can proceed", signal.Symbol);
            }

            _logger.LogInformation("Trade approved for execution: {Symbol}", signal.Symbol);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if trade can be executed for {Symbol}", signal.Symbol);
            return false;
        }
    }
}
